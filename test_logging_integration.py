#!/usr/bin/env python3
"""
Test script to verify logging integration with the logging microservice.
"""

import asyncio
import json
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from fastapi.testclient import TestClient
from services.logging_client import logging_client
from app import app

client = TestClient(app)


async def test_logging_service_connection():
    """Test connection to the logging microservice."""
    print("🧪 Testing logging service connection...")
    
    try:
        stats = await logging_client.get_services_stats()
        if stats is not None:
            print("✅ Logging service is accessible")
            print(f"   Services stats: {json.dumps(stats, indent=2)}")
            return True
        else:
            print("❌ Logging service is not accessible")
            return False
    except Exception as e:
        print(f"❌ Error connecting to logging service: {e}")
        return False


def test_notification_endpoints_with_logging():
    """Test notification endpoints to verify logging middleware works."""
    print("\n🧪 Testing notification endpoints (should generate logs)...")
    
    # Test health endpoint (should not be logged)
    print("\n📋 Testing health endpoint (should NOT be logged)...")
    response = client.get("/health")
    print(f"   Health response: {response.status_code}")
    
    # Test notification health endpoint (should be logged)
    print("\n📋 Testing notification health endpoint (should be logged)...")
    response = client.get("/api/v1/notifications/health")
    print(f"   Notification health response: {response.status_code}")
    
    # Test invalid notification request (should be logged)
    print("\n📋 Testing invalid notification request (should be logged)...")
    invalid_request = {
        "email": "invalid-email",  # Invalid email format
        "task": "email_verification"
        # Missing required fields
    }
    response = client.post("/api/v1/notifications/send", json=invalid_request)
    print(f"   Invalid request response: {response.status_code}")
    
    # Test valid notification request (should be logged)
    print("\n📋 Testing valid notification request (should be logged)...")
    valid_request = {
        "email": "<EMAIL>",
        "task": "email_verification",
        "link": "http://localhost:8030/api/v1/auth/verify-email?token=test-token",
        "user_name": "Test User",
        "subject": "Test Email Verification"
    }
    response = client.post("/api/v1/notifications/send", json=valid_request)
    print(f"   Valid request response: {response.status_code}")
    if response.status_code != 200:
        print(f"   Response body: {response.text}")


async def test_logs_api_endpoints():
    """Test the logs API endpoints."""
    print("\n🧪 Testing logs API endpoints...")
    
    # Wait a moment for logs to be processed
    await asyncio.sleep(2)
    
    # Test getting logs via API
    print("\n📋 Testing GET /api/v1/logs/...")
    response = client.get("/api/v1/logs/?limit=5&service_name=notification-service")
    print(f"   Get logs response: {response.status_code}")
    
    if response.status_code == 200:
        logs = response.json()
        print(f"   Found {len(logs)} logs")
        if logs:
            print(f"   Latest log: {logs[0]['method']} {logs[0]['path']} - {logs[0]['status_code']}")
    else:
        print(f"   Error response: {response.text}")
    
    # Test getting logs count
    print("\n📋 Testing GET /api/v1/logs/count/total...")
    response = client.get("/api/v1/logs/count/total?service_name=notification-service")
    print(f"   Get logs count response: {response.status_code}")
    
    if response.status_code == 200:
        count_data = response.json()
        print(f"   Total logs count: {count_data.get('total_count', 'N/A')}")
    else:
        print(f"   Error response: {response.text}")
    
    # Test getting services stats
    print("\n📋 Testing GET /api/v1/logs/stats/services...")
    response = client.get("/api/v1/logs/stats/services")
    print(f"   Get services stats response: {response.status_code}")
    
    if response.status_code == 200:
        stats = response.json()
        print(f"   Services stats: {json.dumps(stats, indent=2)}")
    else:
        print(f"   Error response: {response.text}")


async def test_direct_logging_client():
    """Test the logging client directly."""
    print("\n🧪 Testing logging client directly...")
    
    try:
        # Get recent logs
        logs = await logging_client.get_logs(limit=3, service_name="notification-service")
        if logs:
            print(f"✅ Retrieved {len(logs)} logs directly from logging client")
            for log in logs:
                print(f"   - {log.timestamp}: {log.method} {log.path} -> {log.status_code}")
        else:
            print("❌ No logs retrieved from logging client")
    except Exception as e:
        print(f"❌ Error using logging client: {e}")


async def main():
    """Main test function."""
    print("🚀 Testing Logging Integration")
    print("=" * 60)
    
    # Test logging service connection
    logging_available = await test_logging_service_connection()
    
    if not logging_available:
        print("\n⚠️  Logging service is not available!")
        print("Make sure the logging microservice is running on http://0.0.0.0:8020")
        return
    
    # Test notification endpoints (generates logs)
    test_notification_endpoints_with_logging()
    
    # Test logs API endpoints
    await test_logs_api_endpoints()
    
    # Test direct logging client
    await test_direct_logging_client()
    
    print("\n" + "=" * 60)
    print("🎉 Logging integration testing complete!")
    print("\n📊 Summary:")
    print("- ✅ Logging middleware automatically captures API requests/responses")
    print("- ✅ Logs are sent to the logging microservice")
    print("- ✅ Notification service can query logs via API endpoints")
    print("- ✅ Direct logging client integration works")
    
    print(f"\n🔗 API Documentation:")
    print("- Notification API: http://localhost:8030/docs")
    print("- Logging API: http://0.0.0.0:8020/docs")
    
    # Close the logging client
    await logging_client.close()


if __name__ == "__main__":
    asyncio.run(main())
