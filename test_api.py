#!/usr/bin/env python3
"""
Simple test script to verify the notification API structure.
This script tests the API without actually sending emails.
"""

import os
import asyncio
import json
from dotenv import load_dotenv

# Load test environment variables
load_dotenv(".env.test")

from fastapi.testclient import TestClient
from app import app

client = TestClient(app)


def test_health_endpoints():
    """Test health check endpoints."""
    print("Testing health endpoints...")
    
    # Test root endpoint
    response = client.get("/")
    print(f"Root endpoint: {response.status_code} - {response.json()}")
    
    # Test health endpoint
    response = client.get("/health")
    print(f"Health endpoint: {response.status_code} - {response.json()}")
    
    # Test API health endpoint
    response = client.get("/api/v1/notifications/health")
    print(f"API health endpoint: {response.status_code} - {response.json()}")


def test_notification_schema():
    """Test notification request schema validation."""
    print("\nTesting notification schema validation...")
    
    # Valid email verification request
    valid_request = {
        "email": "<EMAIL>",
        "task": "email_verification",
        "link": "http://localhost:8000/api/v1/auth/verify-email?token=8d9a1b50-e4f1-47b2-bd3b-37245db5c9db",
        "user_name": "User Name",
        "subject": "Please verify your email address"
    }
    
    print("Valid email verification request:")
    print(json.dumps(valid_request, indent=2))
    
    # Valid password change request
    valid_password_request = {
        "email": "<EMAIL>",
        "task": "change_password",
        "link": "http://localhost:8000/api/v1/auth/change-password?token=...",
        "user_name": "User Name",
        "subject": "Password Change Request"
    }
    
    print("\nValid password change request:")
    print(json.dumps(valid_password_request, indent=2))
    
    # Test invalid request (missing required fields)
    invalid_request = {
        "email": "<EMAIL>",
        "task": "email_verification"
        # Missing required fields: link, user_name, subject
    }
    
    print("\nTesting invalid request (missing fields)...")
    response = client.post("/api/v1/notifications/send", json=invalid_request)
    print(f"Invalid request response: {response.status_code}")
    if response.status_code == 422:
        print("✓ Schema validation working correctly")
    else:
        print("✗ Schema validation issue")


def test_api_documentation():
    """Test API documentation endpoints."""
    print("\nTesting API documentation...")
    
    # Test OpenAPI schema
    response = client.get("/openapi.json")
    print(f"OpenAPI schema: {response.status_code}")
    
    # Test Swagger UI
    response = client.get("/docs")
    print(f"Swagger UI: {response.status_code}")
    
    # Test ReDoc
    response = client.get("/redoc")
    print(f"ReDoc: {response.status_code}")


if __name__ == "__main__":
    print("🚀 Testing Notification Microservice API")
    print("=" * 50)
    
    try:
        test_health_endpoints()
        test_notification_schema()
        test_api_documentation()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")
        print("\nTo start the server, run:")
        print("  uvicorn app:app --reload --host 0.0.0.0 --port 8000")
        print("\nThen visit:")
        print("  - API docs: http://localhost:8000/docs")
        print("  - Health check: http://localhost:8000/health")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
