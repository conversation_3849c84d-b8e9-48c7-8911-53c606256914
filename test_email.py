#!/usr/bin/env python3
"""
Test script to verify email sending functionality.
"""

import asyncio
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from schemas.notification import NotificationRequest, NotificationTask
from services.send_email_service import email_service


async def test_email_verification():
    """Test email verification notification."""
    print("🧪 Testing email verification notification...")
    
    request = NotificationRequest(
        email="<EMAIL>",  # Send to yourself for testing
        task=NotificationTask.EMAIL_VERIFICATION,
        link="http://localhost:8030/api/v1/auth/verify-email?token=test-token-123",
        user_name="Test User",
        subject="Test: Please verify your email address"
    )
    
    try:
        result = await email_service.send_notification_email(request)
        print(f"✅ Email verification result: {result}")
        return result["success"]
    except Exception as e:
        print(f"❌ Email verification failed: {e}")
        return False


async def test_password_change():
    """Test password change notification."""
    print("\n🧪 Testing password change notification...")
    
    request = NotificationRequest(
        email="<EMAIL>",  # Send to yourself for testing
        task=NotificationTask.CHANGE_PASSWORD,
        link="http://localhost:8030/api/v1/auth/change-password?token=test-token-456",
        user_name="Test User",
        subject="Test: Password Change Request"
    )
    
    try:
        result = await email_service.send_notification_email(request)
        print(f"✅ Password change result: {result}")
        return result["success"]
    except Exception as e:
        print(f"❌ Password change failed: {e}")
        return False


async def main():
    """Main test function."""
    print("🚀 Testing Email Service")
    print("=" * 50)
    
    # Test both notification types
    email_verification_success = await test_email_verification()
    password_change_success = await test_password_change()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  Email Verification: {'✅ PASS' if email_verification_success else '❌ FAIL'}")
    print(f"  Password Change: {'✅ PASS' if password_change_success else '❌ FAIL'}")
    
    if email_verification_success and password_change_success:
        print("\n🎉 All email tests passed! Check your inbox.")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")
        print("\nTroubleshooting tips:")
        print("1. Verify your Gmail App Password is correct")
        print("2. Ensure 2FA is enabled on your Gmail account")
        print("3. Check that 'Less secure app access' is disabled (use App Password instead)")
        print("4. Verify your internet connection")


if __name__ == "__main__":
    asyncio.run(main())
