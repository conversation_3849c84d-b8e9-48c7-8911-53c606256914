# Logging Integration

The notification microservice is now integrated with your logging microservice running on `http://0.0.0.0:8020`.

## Features

### 🔄 Automatic Request Logging
- **Middleware**: All API requests and responses are automatically logged
- **Filtering**: Sensitive data (passwords, tokens, etc.) is automatically redacted
- **Performance**: Processing time is tracked for each request
- **Client Info**: IP addresses and user agents are captured

### 📊 Logging API Endpoints
The notification service now provides endpoints to query logs:

- `GET /api/v1/logs/` - Get logs with filtering and pagination
- `GET /api/v1/logs/{log_id}` - Get specific log entry
- `GET /api/v1/logs/count/total` - Get total count of logs
- `GET /api/v1/logs/stats/services` - Get service statistics
- `DELETE /api/v1/logs/cleanup` - Cleanup old logs

### 🛠️ Direct Client Access
- **LoggingClient**: Direct programmatic access to logging service
- **Async Support**: All operations are asynchronous
- **Error Handling**: Graceful handling of logging service unavailability

## Configuration

Add these settings to your `.env` file:

```env
# Logging Service Settings
LOGGING_SERVICE_URL=http://0.0.0.0:8020
ENABLE_REQUEST_LOGGING=true
```

## Usage Examples

### 1. Automatic Logging (Middleware)
All API requests are automatically logged when `ENABLE_REQUEST_LOGGING=true`:

```bash
# This request will be automatically logged
curl -X POST http://localhost:8030/api/v1/notifications/send \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "task": "email_verification",
    "link": "http://localhost:8030/verify?token=123",
    "user_name": "Test User",
    "subject": "Verify Email"
  }'
```

### 2. Query Logs via API
```bash
# Get recent logs for notification service
curl "http://localhost:8030/api/v1/logs/?service_name=notification-service&limit=10"

# Get logs count
curl "http://localhost:8030/api/v1/logs/count/total?service_name=notification-service"

# Get service statistics
curl "http://localhost:8030/api/v1/logs/stats/services"
```

### 3. Direct Client Usage
```python
from services.logging_client import logging_client

# Get recent logs
logs = await logging_client.get_logs(limit=10, service_name="notification-service")

# Get specific log
log = await logging_client.get_log_by_id(123)

# Get service stats
stats = await logging_client.get_services_stats()
```

## Logged Data

### Request Information
- Service name: `notification-service`
- HTTP method and path
- Query parameters
- Request headers (sensitive ones redacted)
- Request body (sensitive data redacted)
- Client IP address
- User agent

### Response Information
- Status code
- Response body (sensitive data redacted)
- Processing time in milliseconds

### Sensitive Data Filtering
The following data is automatically redacted:
- Passwords, tokens, secrets, API keys
- Authorization headers
- Cookie values
- Any field containing "password", "token", "secret", "key", "auth"

## Testing

Run the integration test:

```bash
python test_logging_integration.py
```

This will:
1. Test connection to the logging service
2. Make sample API requests (generates logs)
3. Query logs via the API endpoints
4. Test direct client access

## API Documentation

- **Notification Service**: http://localhost:8030/docs
- **Logging Service**: http://0.0.0.0:8020/docs

## Architecture

```
┌─────────────────────┐    ┌─────────────────────┐
│  Notification API   │    │   Logging Service   │
│                     │    │                     │
│  ┌───────────────┐  │    │  ┌───────────────┐  │
│  │   Middleware  │──┼────┼─▶│   Log Storage │  │
│  └───────────────┘  │    │  └───────────────┘  │
│                     │    │                     │
│  ┌───────────────┐  │    │  ┌───────────────┐  │
│  │ Logging Client│──┼────┼─▶│   Query API   │  │
│  └───────────────┘  │    │  └───────────────┘  │
│                     │    │                     │
│  ┌───────────────┐  │    │                     │
│  │   Logs API    │──┼────┼─────────────────────┤
│  └───────────────┘  │    │                     │
└─────────────────────┘    └─────────────────────┘
```

## Benefits

1. **Centralized Logging**: All microservices log to the same service
2. **Automatic Capture**: No manual logging code needed for API requests
3. **Security**: Sensitive data is automatically filtered
4. **Performance Monitoring**: Request processing times are tracked
5. **Debugging**: Easy to trace requests across services
6. **Analytics**: Service usage statistics and patterns
