import json
import time
import logging
from typing import Callable, Dict, Any, Optional

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import StreamingResponse

from services.logging_client import logging_client, LogCreate
from core.config import settings


logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to log all API requests and responses to the logging microservice."""
    
    def __init__(self, app, service_name: str = "notification-service"):
        super().__init__(app)
        self.service_name = service_name
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and log to logging microservice."""
        
        # Skip logging for health checks and docs
        if request.url.path in ["/health", "/", "/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)
        
        # Record start time
        start_time = time.time()
        
        # Extract request information
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent")
        method = request.method
        path = str(request.url.path)
        query_params = dict(request.query_params) if request.query_params else None
        
        # Get request headers (filter sensitive ones)
        headers = self._filter_headers(dict(request.headers))
        
        # Read request body
        request_body = await self._get_request_body(request)
        
        # Process the request
        response = await call_next(request)
        
        # Calculate processing time
        processing_time = int((time.time() - start_time) * 1000)  # Convert to milliseconds
        
        # Get response body
        response_body = await self._get_response_body(response)
        
        # Create log entry
        log_entry = LogCreate(
            service_name=self.service_name,
            method=method,
            path=path,
            query_params=query_params,
            request_body=request_body,
            response_body=response_body,
            status_code=response.status_code,
            processing_time=processing_time,
            client_ip=client_ip,
            user_agent=user_agent,
            headers=headers
        )
        
        # Send log to logging microservice (async, don't wait for response)
        try:
            await logging_client.create_log_entry(log_entry)
        except Exception as e:
            logger.error(f"Failed to send log to logging microservice: {str(e)}")
            # Don't fail the request if logging fails
        
        return response
    
    def _get_client_ip(self, request: Request) -> Optional[str]:
        """Extract client IP address from request."""
        # Check for forwarded headers first (for reverse proxies)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return None
    
    def _filter_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """Filter out sensitive headers."""
        sensitive_headers = {
            "authorization", "cookie", "x-api-key", "x-auth-token",
            "password", "secret", "token"
        }
        
        filtered = {}
        for key, value in headers.items():
            key_lower = key.lower()
            if any(sensitive in key_lower for sensitive in sensitive_headers):
                filtered[key] = "[REDACTED]"
            else:
                filtered[key] = value
        
        return filtered
    
    async def _get_request_body(self, request: Request) -> Optional[str]:
        """Extract request body as string."""
        try:
            # Only read body for POST, PUT, PATCH requests
            if request.method in ["POST", "PUT", "PATCH"]:
                body = await request.body()
                if body:
                    # Try to decode as JSON for better formatting
                    try:
                        json_body = json.loads(body.decode("utf-8"))
                        # Filter sensitive data
                        filtered_body = self._filter_sensitive_data(json_body)
                        return json.dumps(filtered_body, separators=(',', ':'))
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        # If not JSON, return as string (truncated if too long)
                        body_str = body.decode("utf-8", errors="ignore")
                        return body_str[:1000] + "..." if len(body_str) > 1000 else body_str
            return None
        except Exception as e:
            logger.error(f"Error reading request body: {str(e)}")
            return None
    
    async def _get_response_body(self, response: Response) -> Optional[str]:
        """Extract response body as string."""
        try:
            # Only log response body for successful requests and errors
            if response.status_code < 500:
                if hasattr(response, "body"):
                    body = response.body
                    if body:
                        try:
                            # Try to decode as JSON
                            if isinstance(body, bytes):
                                body_str = body.decode("utf-8")
                            else:
                                body_str = str(body)
                            
                            json_body = json.loads(body_str)
                            # Filter sensitive data
                            filtered_body = self._filter_sensitive_data(json_body)
                            return json.dumps(filtered_body, separators=(',', ':'))
                        except (json.JSONDecodeError, UnicodeDecodeError):
                            # If not JSON, return as string (truncated if too long)
                            body_str = body.decode("utf-8", errors="ignore") if isinstance(body, bytes) else str(body)
                            return body_str[:1000] + "..." if len(body_str) > 1000 else body_str
            return None
        except Exception as e:
            logger.error(f"Error reading response body: {str(e)}")
            return None
    
    def _filter_sensitive_data(self, data: Any) -> Any:
        """Recursively filter sensitive data from JSON objects."""
        if isinstance(data, dict):
            filtered = {}
            for key, value in data.items():
                key_lower = key.lower()
                if any(sensitive in key_lower for sensitive in ["password", "token", "secret", "key", "auth"]):
                    filtered[key] = "[REDACTED]"
                else:
                    filtered[key] = self._filter_sensitive_data(value)
            return filtered
        elif isinstance(data, list):
            return [self._filter_sensitive_data(item) for item in data]
        else:
            return data
