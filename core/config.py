import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""

    # Application settings
    app_name: str = Field(default="Notification Service", env="APP_NAME")
    debug: bool = Field(default=False, env="DEBUG")

    # Gmail SMTP settings
    smtp_server: str = Field(default="smtp.gmail.com", env="SMTP_SERVER")
    smtp_port: int = Field(default=587, env="SMTP_PORT")
    smtp_username: str = Field(..., env="SMTP_USERNAME")
    smtp_password: str = Field(..., env="SMTP_PASSWORD")
    smtp_use_tls: bool = Field(default=True, env="SMTP_USE_TLS")

    # Email settings
    from_email: str = Field(..., env="FROM_EMAIL")
    from_name: str = Field(default="Notification Service", env="FROM_NAME")

    # Template settings
    template_dir: str = Field(default="templates", env="TEMPLATE_DIR")

    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()